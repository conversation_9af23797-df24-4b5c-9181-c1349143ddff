"""
Utilitaires pour la génération d'URLs dynamiques
"""
from flask import request
import socket


def get_server_ip():
    """
    Obtient l'adresse IP du serveur accessible depuis le réseau local
    """
    try:
        # Créer une socket pour obtenir l'IP locale
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "127.0.0.1"


def get_base_url():
    """
    Génère l'URL de base en fonction de la requête actuelle
    Utilise l'IP et le port de la requête courante
    """
    # Obtenir l'host et le port de la requête actuelle
    host = request.host
    
    # Si on est sur localhost/127.0.0.1, utiliser l'IP réseau
    if '127.0.0.1' in host or 'localhost' in host:
        server_ip = get_server_ip()
        port = request.environ.get('SERVER_PORT', '5000')
        return f"http://{server_ip}:{port}"
    
    # Sinon utiliser l'host de la requête
    return f"http://{host}"


def generate_site_url(subdomain):
    """
    Génère l'URL complète pour un site de commande en ligne
    
    Args:
        subdomain (str): Le sous-domaine du site
        
    Returns:
        str: L'URL complète du site
    """
    base_url = get_base_url()
    
    # Si on utilise une IP (pas de domaine), on utilise un paramètre de requête
    if any(char.isdigit() for char in base_url.split('://')[1].split(':')[0]):
        # C'est une IP, utiliser un paramètre de requête
        return f"{base_url}/online-ordering?site={subdomain}"
    else:
        # C'est un domaine, utiliser le sous-domaine
        return f"http://{subdomain}.{base_url.split('://')[1]}"


def get_current_site_url():
    """
    Obtient l'URL du site actuel basée sur la requête
    """
    return get_base_url()


def is_mobile_access():
    """
    Détecte si l'accès se fait depuis un appareil mobile
    en analysant l'IP de la requête
    """
    host = request.host
    remote_addr = request.environ.get('REMOTE_ADDR', '')
    
    # Si l'IP de la requête est différente de l'host, c'est probablement un accès mobile
    if '127.0.0.1' not in host and 'localhost' not in host:
        return True
    
    return False
