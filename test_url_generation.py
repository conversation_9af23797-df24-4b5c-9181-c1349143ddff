#!/usr/bin/env python3
"""
Script de test pour vérifier la génération d'URLs dynamiques
"""

import sys
import os

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from config import Config

def test_url_generation():
    """Test de la génération d'URLs"""
    app = create_app(Config)
    
    with app.test_request_context('http://**************:5000/settings/online_ordering'):
        from app.utils.url_helpers import generate_site_url, get_base_url, get_server_ip
        
        print("=== Test de génération d'URLs ===")
        print(f"IP du serveur détectée: {get_server_ip()}")
        print(f"URL de base: {get_base_url()}")
        print(f"URL pour le site 'azerty1': {generate_site_url('azerty1')}")
        print()
        
    with app.test_request_context('http://127.0.0.1:5000/settings/online_ordering'):
        from app.utils.url_helpers import generate_site_url, get_base_url, get_server_ip
        
        print("=== Test depuis localhost ===")
        print(f"IP du serveur détectée: {get_server_ip()}")
        print(f"URL de base: {get_base_url()}")
        print(f"URL pour le site 'azerty1': {generate_site_url('azerty1')}")
        print()
        
    with app.test_request_context('http://azerty1.lvh.me:5000/'):
        from app.utils.url_helpers import generate_site_url, get_base_url, get_server_ip
        
        print("=== Test depuis sous-domaine lvh.me ===")
        print(f"IP du serveur détectée: {get_server_ip()}")
        print(f"URL de base: {get_base_url()}")
        print(f"URL pour le site 'azerty1': {generate_site_url('azerty1')}")
        print()

if __name__ == '__main__':
    test_url_generation()
